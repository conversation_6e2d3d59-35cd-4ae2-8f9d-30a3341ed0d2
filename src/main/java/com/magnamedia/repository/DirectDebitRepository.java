package com.magnamedia.repository;

import com.magnamedia.controller.DirectDebitCancelationToDoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.module.type.DirectDebitCancellationToDoReason;
import com.magnamedia.module.type.DirectDebitCategory;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.module.type.DirectDebitType;
import com.magnamedia.service.DirectDebitCancellationService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Dec 10, 2018
 * 
 */
@Repository
public interface DirectDebitRepository extends BaseRepository<DirectDebit> {

    // ACC-1135
    List<DirectDebit> findByContractPaymentTerm_Contract(Contract contract);

    List<DirectDebit> findByContractPaymentTerm_ContractAndDirectDebitRejectionToDoNotNull(Contract contract);

    List<DirectDebit> findByDirectDebitRejectionToDo(DirectDebitRejectionToDo debitRejectionToDo);

    List<DirectDebit> findByDirectDebitBouncingRejectionToDo(DirectDebitRejectionToDo debitRejectionToDo);

    // ACC-1435
    List<DirectDebit> findByContractPaymentTerm(ContractPaymentTerm contractPaymenTerm);

    List<DirectDebit> findByContractPaymentTermAndStatusNotInAndMStatusNotIn(
            ContractPaymentTerm cpt, List<DirectDebitStatus> s1, List<DirectDebitStatus> s2);

    @Query("SELECT count(d.id) > 0 FROM ContractPayment cp " +
            "JOIN cp.directDebit d " +
            "WHERE d.contractPaymentTerm = ?1 AND cp.paymentType.code = ?2 AND " +
                "d.status NOT IN ?2 AND d.MStatus NOT IN ?3")
    boolean existsByCptAndStatusAndType(ContractPaymentTerm cpt, String type, List<DirectDebitStatus> s);

    List<DirectDebit> findByContractPaymentTermAndCategory(ContractPaymentTerm contractPaymenTerm, DirectDebitCategory category);
    
    List<DirectDebit> findByContractPaymentTermAndCategoryOrderByCreationDateDesc(ContractPaymentTerm contractPaymenTerm, DirectDebitCategory category);

    List<DirectDebit> findByContractPaymentTermAndAddedByOecFlowOrderByCreationDate(ContractPaymentTerm contractPaymenTerm, Boolean addedByOecFlow);

    List<DirectDebit> findByContractPaymentTerm(ContractPaymentTerm contractPaymenTerm, Sort sort);

    List<DirectDebit> findByContractPaymentTermAndCreationDateGreaterThanEqual(
            ContractPaymentTerm contractPaymentTerm, Date creationDate);

    // ACC-3597
    List<DirectDebit> findByContractPaymentTermAndStartDateGreaterThanEqual(ContractPaymentTerm contractPaymenTerm, Date startDate);
    List<DirectDebit> findByContractPaymentTermAndStartDateLessThan(ContractPaymentTerm contractPaymenTerm , Date startDate);

    @Query("select count(ddf) > 0 " +
            "from DirectDebitFile ddf " +
            "where ddf.directDebit.contractPaymentTerm = :term and " +
                "((:startDate between ddf.startDate and ddf.expiryDate) or " +
                    "(:endDate between ddf.startDate and ddf.expiryDate) or " +
                    "(ddf.startDate between :startDate and :endDate)) and " +
                "ddf.ddStatus not in ('CANCELED', 'REJECTED', 'PENDING_FOR_CANCELLATION') and ddf.ddFrequency = 'MONTHLY' and " +
                "(ddf.ddStatus <> 'CONFIRMED' or (ddf.ddStatus = 'CONFIRMED' and " +
                    "not exists(select 1 from DirectDebitCancelationToDo ddctd where ddctd.directDebitFile = ddf)))")
    Boolean getOverlappingDD(@Param("term") ContractPaymentTerm contractPaymentTerm,
                         @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    // ACC-2564
    @Query("select count(dd) > 0 " +
            "from DirectDebit dd " +
            "where dd.contractPaymentTerm = :term and " +
                "((:startDate between dd.startDate and dd.expiryDate) or " +
                "(:endDate between dd.startDate and dd.expiryDate) or " +
                "(dd.startDate between :startDate and :endDate)) and " +
                "dd.status = 'IN_COMPLETE' and dd.MStatus IN ('IN_COMPLETE', 'NOT_APPLICABLE') and dd.type = 'MONTHLY' and " +
                "(not exists(select 1 from DirectDebitFile ddf where ddf.directDebit = dd))")
    Boolean getOverlappingInCompleteDDs(@Param("term") ContractPaymentTerm contractPaymentTerm,
                                    @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Query("SELECT dd FROM DirectDebit dd where dd.contractPaymentTerm = :term and (status = 'IN_COMPLETE' or dd.status = 'PENDING_DATA_ENTRY') and  " +
            "exists (SELECT 1 FROM DirectDebitFile DDF where DDF.status = 'NOT_COMPLETED' and DDF.directDebit = dd) " +
            "and (NOT EXISTS (SELECT 1 FROM Payment p where p.directDebitId = dd.id and p.bouncedFlowPausedForReplacement = true)) ")
    List<DirectDebit> getNotCompletedDirectDebits(@Param("term") ContractPaymentTerm contractPaymentTerm);

    @Query("SELECT dd FROM DirectDebit dd where dd.contractPaymentTerm = :term and status = 'PENDING' OR status = 'CONFIRMED' ORDER BY startDate DESC")
    List<DirectDebit> getPendingOrConfimDDs(@Param("term") ContractPaymentTerm contractPaymentTerm, Pageable pageable);

    @Query("select ddf.ibanNumber, ddf.bankName, min(ddf.creationDate) " +
            "from DirectDebitFile ddf " +
            "join ddf.directDebit dd " +
            "where dd.contractPaymentTerm.contract = ?1 and ddf.bankName is not null and " +
                "((dd.category = 'B' and dd.status NOT IN  ('IN_COMPLETE', 'PENDING_DATA_ENTRY')) or " +
                    "(dd.category = 'A' and dd.MStatus NOT IN  ('IN_COMPLETE', 'PENDING_DATA_ENTRY'))) " +
            "group by ddf.ibanNumber " +
            "order by min(ddf.creationDate) asc")
    List<Object[]> getLatestBankInfoByContract(Contract ct);

    DirectDebit findFirstByContractPaymentTermAndStartDateBetween(ContractPaymentTerm cpt, Date start, Date end);

    boolean existsByContractPaymentTerm(ContractPaymentTerm contractPaymentTerm);

    @Query("SELECT dd FROM DirectDebit dd where dd.contractPaymentTerm = :term and dd.startDate < :firstDate " +
            "and dd.expiryDate < :firstDate and (dd.expiryDate >= :secondDate or dd.startDate >= :secondDate)")
    List<DirectDebit> getDDForFullPaymentsToMove(
            @Param("term") ContractPaymentTerm contractPaymentTerm,
            @Param("firstDate") Date firstDate, @Param("secondDate") Date secondDate);

    @Query("SELECT dd FROM DirectDebit dd where dd.contractPaymentTerm = :term " +
            "and (dd.expiryDate >= :secondDate or dd.startDate >= :secondDate)")
    List<DirectDebit> getDDForFullPaymentsToMove(
            @Param("term") ContractPaymentTerm contractPaymentTerm,
            @Param("secondDate") Date secondDate);

    @Query("SELECT dd FROM DirectDebit dd where dd.contractPaymentTerm = :term and dd.startDate >= :secondDate " +
            "and dd.expiryDate >= :secondDate and (dd.expiryDate >= :firstDate or dd.startDate >= :firstDate)")
    List<DirectDebit> getDDForDiscountPaymentsToMove(
            @Param("term") ContractPaymentTerm contractPaymentTerm,
            @Param("firstDate") Date firstDate, @Param("secondDate") Date secondDate);

    @Query("SELECT dd FROM DirectDebit dd " +
            "where dd.contractPaymentTerm = :term and " +
                "NOT EXISTS (select 1 from ContractPayment CP where CP.directDebit = dd)")
    List<DirectDebit> getWithoutPaymentsDD(@Param("term") ContractPaymentTerm contractPaymentTerm);

    @Query("select dd from DirectDebit dd " +
            "where dd.status = com.magnamedia.module.type.DirectDebitStatus.IN_COMPLETE " +
                "and dd.id not in ?1 and dd.contractPaymentTerm = ?2")
    List<DirectDebit> findByStatusAndIdNotInAndContractPaymentTerm(
            List<Long> Ids, ContractPaymentTerm contractPaymentTerm);

    @Override
    default void delete(DirectDebit directDebit) {
        if (directDebit != null && !directDebit.getIsDeleted()) {
            if (directDebit.getStatus() == DirectDebitStatus.IN_COMPLETE && directDebit.getMStatus() == DirectDebitStatus.IN_COMPLETE) {
                DirectDebitCancelationToDoController debitCancelationToDoController = Setup.getApplicationContext().getBean(DirectDebitCancelationToDoController.class);
                Setup.getApplicationContext().getBean(DirectDebitCancellationService.class)
                    .cancelWholeDD(directDebit,  DirectDebitCancellationToDoReason.IN_COMPLETE_DDS_DELETING);
            }

            Map ddMap = new HashMap();
            ddMap.put("id", directDebit.getId());
            ddMap.put("isDeleted", Boolean.TRUE);
            Logger.getLogger(DirectDebitRepository.class.getName()).log(Level.SEVERE, "set DD#" + directDebit.getId() + " as IS_DELETED = True");
            Setup.getApplicationContext().getBean(InterModuleConnector.class).postJsonAsync("accounting/directDebit/update", ddMap);
            Logger.getLogger(DirectDebitRepository.class.getName()).log(Level.SEVERE, "IMC Request has been Created");
        }
    }

    @Query("select dd from DirectDebit dd where dd.contractPaymentTerm = :cpt "
            + "and dd.status = :ddStatus and dd.ddBankInfoGroup = :bankInfoGroup "
            + "and (NOT EXISTS (SELECT 1 FROM Payment p where p.directDebitId = dd.id and p.bouncedFlowPausedForReplacement = true))")
    List<DirectDebit> getByContractPaymentTermAndStatusAndDdBankInfoGroup(
            @Param("cpt") ContractPaymentTerm contractPaymentTerm,
            @Param("ddStatus") DirectDebitStatus directDebitStatus,
            @Param("bankInfoGroup") Long bankInfoGroup);

    @Query("SELECT dd FROM DirectDebit dd where dd.contractPaymentTerm = :term and " +
            "(dd.status = :ddStatus or dd.MStatus = :ddStatus) and dd.ddBankInfoGroup = :bankInfoGroup")
    List<DirectDebit> getByContractPaymentTermAnd_StatusOrMStatus_AndDdBankInfoGroup(
            @Param("term") ContractPaymentTerm contractPaymentTerm,
            @Param("ddStatus") DirectDebitStatus directDebitStatus,
            @Param("bankInfoGroup") Long bankInfoGroup);

    List<DirectDebit> getByContractPaymentTermAndDdBankInfoGroup(
            ContractPaymentTerm contractPaymentTerm, Long bankInfoGroup);

    // ACC-1689
    List<DirectDebit> getByContractPaymentTermAndExpiryDateIsGreaterThan(ContractPaymentTerm contractPaymentTerm, Date date);
    
    // ACC-3597
    List<DirectDebit> findByContractPaymentTermAndExpiryDateIsGreaterThanAndCategoryAndStatusInOrderByStartDate(
            ContractPaymentTerm contractPaymentTerm, Date date, DirectDebitCategory category, List<DirectDebitStatus> status);

    // ACC-1689
    List<DirectDebit> getByContractPaymentTermAndTypeInAndExpiryDateIsGreaterThan(
            ContractPaymentTerm contractPaymentTerm, List<DirectDebitType> directDebitTypes, Date date);

    boolean existsByIbanNumberAndEidAndAccountNameAndConfirmedBankInfoAndContractPaymentTerm_Contract_Client(
            String iban, String eid, String accountName, boolean confirmedBankInfo, Client client);

    // ACC-2847
    @Query(nativeQuery = true,
            value = "SELECT D.ID, D.ACCOUNT_NAME, D.IBAN_NUMBER, I.CODE " +
                    "FROM CONTRACTPAYMENTTERMS CPT " +
                    "INNER JOIN CONTRACTS CT ON CPT.CONTRACT_ID = CT.ID " +
                    "INNER JOIN DIRECTDEBITS D ON D.CONTRACT_PAYMENT_TERM_ID = CPT.ID " +
                    "LEFT JOIN PICKLISTS_ITEMS I ON I.ID = D.BANK_ID " +
                    "INNER JOIN DIRECTDEBITS_REVISIONS R ON R.ID = D.ID AND  " +
                        "((R.STATUS = 'CONFIRMED' AND R.STATUS_MODIFIED = 1) OR " +
                        "(R.M_STATUS = 'CONFIRMED' AND R.M_STATUS_MODIFIED = 1)) " +
                    "WHERE CT.CLIENT_ID = ?1 AND D.ACCOUNT_NAME IS NOT NULL AND D.IBAN_NUMBER IS NOT NULL " +
                    "ORDER BY R.LAST_MODIFICATION_DATE DESC LIMIT 1")
    List<Object[]> getClientAccountInfo(Long clientId);

    @Query("SELECT count(dd.id) > 0 FROM DirectDebit dd " +
            "where dd.contractPaymentTerm.contract.client.id = :clientId and dd.bank in :banks ")
    boolean existDirectDebitByClientAndBanks(@Param("clientId") Long clientId, @Param("banks") List<PicklistItem> banks);

    // ACC-1622
    Boolean existsByContractPaymentTerm_Contract(Contract contract);

    // ACC-1721
    @Query("SELECT dd FROM DirectDebit dd where dd.contractPaymentTerm.contract.isScheduledForTermination = false and " +
            "exists ( select ddf from DirectDebitFile ddf where ddf.directDebit = dd and " +
            "ddf.forBouncingPayment = true and ddf.creationDate < :date and " +
            "ddf.ddStatus = com.magnamedia.module.type.DirectDebitStatus.IN_COMPLETE and " +
            "ddf.directDebitSignature is null) and " +
            "dd.id not in :ids and " +
            "(exists (" +
            "select p from Payment p where p.directDebitId = dd.id and p.status = com.magnamedia.module.type.PaymentStatus.BOUNCED and " +
            "(p.replaced is null or p.replaced = false )))")
    Page<DirectDebit> findDDsForBouncedPayments(Pageable pageable, @Param("date") Date date, @Param("ids") List<Long> ids);

    List<DirectDebit> findByStatusAndDirectDebitRejectionToDoNotNullAndContractPaymentTerm(
            DirectDebitStatus directDebitStatus, ContractPaymentTerm contractPaymentTerm);

    Boolean existsByStatusAndDirectDebitRejectionToDoNotNullAndContractPaymentTerm(
            DirectDebitStatus directDebitStatus, ContractPaymentTerm contractPaymentTerm);

    List<DirectDebit> findByDirectDebitBouncingRejectionToDoNotNullAndContractPaymentTerm(ContractPaymentTerm contractPaymentTerm);

    @Query("SELECT count(d.id) > 0 FROM DirectDebit d " +
            "WHERE d.contractPaymentTerm = ?1 AND (d.status = 'IN_COMPLETE' OR d.MStatus = 'IN_COMPLETE')")
    Boolean existsIncompleteDDsByCpt(ContractPaymentTerm cpt);

    List<DirectDebit> findByContractPaymentTermAndStatusAndCategoryAndManualDdfFileIsNullAndExpiryDateGreaterThanEqual(
            ContractPaymentTerm cpt, DirectDebitStatus status, DirectDebitCategory category, Date expiryDate);

    // ACC-1588
    @Query("select dd from DirectDebit dd left join dd.contractPaymentTerm cpt where cpt.contract.id = :contractId " +
            "and (:activeCptOnly = false or cpt.isActive = true) order by cpt.id, dd.startDate")
    List<DirectDebit> findByContractAndActiveCPT(@Param("contractId") Long contractId, @Param("activeCptOnly") boolean activeCptOnly);

    // ACC-2024
    boolean existsByInCompleteDDTrialsGreaterThanAndContractPaymentTerm_Contract(Integer maxTrials, Contract contract);

    @Query("select dd from DirectDebit dd where (dd.status = :status or dd.MStatus = :status) and " +
            "dd.creationDate < :date and dd.directDebitRejectionToDo is null and dd.directDebitBouncingRejectionToDo is null and " +
            "dd.contractPaymentTerm.contract.isScheduledForTermination = false and " +
            "(dd.contractPaymentTerm.isIBANRejected = true or dd.contractPaymentTerm.isEidRejected = true or dd.contractPaymentTerm.isAccountHolderRejected = true) and " +
            "dd.contractPaymentTerm.contract.status not in (com.magnamedia.module.type.ContractStatus.CANCELLED, com.magnamedia.module.type.ContractStatus.EXPIRED) and " +
            "dd.id not in :ids " +
            "group by dd.ddBankInfoGroup")
    Page<DirectDebit> findByStatusOrMStatusAndCreationDateLessThanWithNoCashPaymentsAndIdNotIn(@Param("status") DirectDebitStatus ddStatus, @Param("date") Date creationDate, @Param("ids") List<Long> ids, Pageable pageable);

    // ACC-1778
    boolean existsByContractPaymentTermAndStatusAndStartDateGreaterThan(ContractPaymentTerm contractPaymentTerm, DirectDebitStatus status, Date startDate);

    @Query("select dd from DirectDebit dd where dd.contractPaymentTerm = ?1 and " +
            "(dd.status in ?2 or dd.MStatus in ?2) and dd.status not in ?3 and dd.MStatus not in ?3 " +
            "ORDER BY dd.creationDate")
    List<DirectDebit> findByContractPaymentTermAndStatusIn(
            ContractPaymentTerm contractPaymentTerm,
            List<DirectDebitStatus> statuses,
            List<DirectDebitStatus> invalidStatuses);

    @Query("select dd from DirectDebit dd " +
            "where dd.contractPaymentTerm = ?1 and " +
                "((dd.category = 'B' and dd.status in ?2) or (dd.category = 'A' and dd.MStatus in ?2)) " +
            "ORDER BY dd.startDate")
    List<DirectDebit> findByContractPaymentTermAndStatusIn(
            ContractPaymentTerm contractPaymentTerm,
            List<DirectDebitStatus> statuses);

    @Query("select count(dd.id) > 0 from ContractPayment cp " +
            "join cp.directDebit dd " +
            "join DirectDebitFile ddf on ddf.directDebit = dd " +
            "where dd.contractPaymentTerm.contract = ?1 and dd.contractPaymentTerm.isActive = true and " +
                "((dd.category = 'B' and dd.status in ?2 and ddf.ddMethod = 'AUTOMATIC') or " +
                "(dd.category = 'A' and dd.MStatus in ?2 and ddf.ddMethod = 'MANUAL')) and " +
                "cp.date >= ?3 and cp.date <= ?4 and " +
                "not exists (select 1 from DirectDebitCancelationToDo todo " +
                    "where todo.directDebitFile = ddf and todo.stopped = false and todo.completed = false)")
    boolean existsByContractAndStatusInAndDateBetween(
            Contract contract, List<DirectDebitStatus> statuses, Date d1, Date d2);

    @Query("select dd from DirectDebit dd " +
            "where dd.contractPaymentTerm = ?1 and ((dd.category = 'A' and dd.MStatus not in ?2) or " +
                "(dd.category = 'B' and dd.status not in ?2 or (dd.MStatus not in ?2 and dd.MStatus <> 'NOT_APPLICABLE')))")
    List<DirectDebit> findByContractPaymentTermAndStatusNotIn(
            ContractPaymentTerm contractPaymentTerm,
            List<DirectDebitStatus> statuses);

    List<DirectDebit> findByImageForDD(DirectDebit directDebit);

    boolean existsByContractPaymentTermAndImageForDD(ContractPaymentTerm cpt, DirectDebit directDebit);

    List<DirectDebit> findByIdIn(List<Long> ids);

    @Query(value = "CALL getOCRStatistics(:START_DATE, :TAG);", nativeQuery = true)
    List<Map<String, Object>> getOCRStatistics(@Param("START_DATE") Date startDate, @Param("TAG") String tag);

    DirectDebit findTopByContractPaymentTermOrderById(ContractPaymentTerm contractPaymentTerm);

    DirectDebit findTopByContractPaymentTerm_Contract_ClientAndConfirmedBankInfoTrueOrderByConfirmBankInfoDate(Client client);

    List<DirectDebit> findByContractPaymentTermAndRequiredAfterSwitchingAndIdNotIn(ContractPaymentTerm contractPaymentTerm, boolean requiredAfterSwitching, List<Long> ids);

    @Query("select dd from DirectDebit dd " +
            "where dd.contractPaymentTerm.contract = ?1 and dd.id not in ?2 and status not in ?3 and MStatus not in ?3 " +
            "and (dd.directDebitBouncingRejectionToDo is not null or dd.directDebitRejectionToDo is not null)")
    List<DirectDebit> findByContractPaymentTermContractAndHasRejectionToDoAndIdNotInAndStatusNotIn(
            Contract contract, List<Long> ids, List<DirectDebitStatus> statuses);

    @Query(value = "SELECT distinct  c.CONTRACT_ID from DIRECTDEBITS ddm  inner join CONTRACTPAYMENTTERMS c on "+
            " c.ID = ddm.CONTRACT_PAYMENT_TERM_ID inner join DIRECTDEBITFILES dd1 on dd1.DIRECT_DEBIT_ID = ddm.ID " +
            "JOIN (SELECT ID,DIRECT_DEBIT_ID,DD_METHOD,STATUS_CHANGE_DATE  FROM  DIRECTDEBITFILES  where " +
            "DD_STATUS = 'CONFIRMED' and STATUS_CHANGE_DATE  <= ?1 ) AS dd2 ON " +
            "dd1.ID <> dd2.ID " +
            "and dd1.DIRECT_DEBIT_ID = dd2.DIRECT_DEBIT_ID " +
            "and dd2.DD_METHOD = dd1.DD_METHOD " +
            "WHERE dd1.DD_STATUS = 'CONFIRMED' and dd1.STATUS_CHANGE_DATE <= ?1 "
            ,nativeQuery = true)
    List<String> findDuplicateApproveDDF(String ddfStatusChangeDate);

    @Query("select count(d.id) > 0 " +
            "from DirectDebitFile ddf " +
            "join ddf.directDebit d " +
            "where d.contractPaymentTerm = ?1 and d.status = 'CONFIRMED' and d.category = 'B' and ddf.ddMethod = 'AUTOMATIC' and " +
                "(d.startDate between ?2 and ?3 or (d.startDate <= ?2 and d.expiryDate > ?2)) and " +
                  "not exists (select 1 from DirectDebitCancelationToDo todo " +
                      "where todo.directDebitFile = ddf and todo.stopped = false and todo.completed = false)")
    boolean existOverLappingDDBs(ContractPaymentTerm cpt, Date st, Date e);

    @Query("SELECT COUNT(p.id) > 0 FROM Payment p " +
            "INNER JOIN DirectDebit d ON p.directDebitId = d.id " +
            "INNER JOIN d.contractPaymentTerm cpt " +
            "WHERE cpt.contract = ?1 AND cpt.isActive = 1 " +
                "AND ((d.category = 'B' AND d.status IN ?2 AND d.MStatus IN ?2) " +
                    "OR (d.category = 'A' AND d.MStatus IN ?2)) " +
                "AND p.id = (SELECT MIN(id) FROM Payment " +
                            "WHERE contract = ?1 AND status IN ('PDC', 'PRE_PDP'))")
    boolean existPendingDdForFollowingPayment(Contract ct, List<DirectDebitStatus> s);

    @Query("SELECT distinct new map(cpt.paymentTermConfig.name as cptName, ddm.amount as ddAmount," +
            " cp.paymentType.name as ddType, ddm.lastModificationDate as ddCancellationDate," +
            " cpt.lastModificationDate as dateCptBecomeInactive)  " +
            "FROM DirectDebit ddm " +
            "inner join ddm.contractPaymentTerm cpt " +
            "left join ddm.contractPayments cp  " +
            "WHERE cpt.contract.id = ?1 AND cpt.id <>  ?2 " +
            "and (ddm.status = com.magnamedia.module.type.DirectDebitStatus.CANCELED " +
            "   Or ddm.MStatus = com.magnamedia.module.type.DirectDebitStatus.CANCELED)")
    Page<Map<String, Object>> getDdfForInactiveCpt(Long contractId, Long activeCptId, Pageable pageable);

    @Query("select ddm from DirectDebit ddm " +
            "where ddm.contractPaymentTerm.id = ?1 and ddm.category = 'B' " +
                "and (ddm.MStatus in ('IN_COMPLETE', 'REJECTED') " +
                "or ddm.status in ('IN_COMPLETE', 'REJECTED')) " +
            "ORDER BY CASE " +
            "WHEN ddm.MStatus = 'REJECTED' THEN 0 " +
            "WHEN ddm.MStatus = 'IN_COMPLETE' THEN 1 " +
            "ELSE 2 end, ddm.id desc")
    List<DirectDebit> findDDBByContractPaymentTermAndStatus(Long cptId);

    @Query("Select count(d.id) > 0 from DirectDebit d " +
           "where d.contractPaymentTerm.id = ?1 and (d.status = 'CONFIRMED' or d.MStatus = 'CONFIRMED') " +
                "and ((d.category = 'B' and d.startDate <= ?2 and d.expiryDate >= ?2) " +
                "or (d.category = 'A' and d.startDate = ?2 ))")
    boolean existsDdConfirmedAndCoverDate(Long cptId, Date date);

    @Query("SELECT d FROM ContractPayment cp JOIN cp.directDebit d " +
            "WHERE d.contractPaymentTerm.contract = ?1 AND d.category = 'A' AND " +
                "d.status NOT IN ?2 AND d.MStatus NOT IN ?2 AND " +
                "month(d.startDate) = month(?3) AND year(d.startDate) = year(?3) AND  " +
                "cp.amount = ?4 AND cp.paymentType = ?5")
    List<DirectDebit> findForAcc5241(
            Contract ct, List<DirectDebitStatus> s, Date d, Double amount, PicklistItem t);

    @Query("SELECT COUNT (d.id) > 0 " +
            "FROM DirectDebit d " +
            "where d.contractPaymentTerm = ?1 and d.category in ?2 " +
                "and (d.status in ?3 or d.MStatus in ?3) ")
    Boolean existsByContractPaymentTermAndCategoryBAndStatusIn(
            ContractPaymentTerm cpt, List<DirectDebitCategory> categories, List<DirectDebitStatus> statuses);

    @Query("select d.amount from DirectDebit d " +
            "where d.id = " +
                "(select max(d1.id) from ContractPayment cp " +
                "join cp.directDebit as d1 " +
                "where d1.contractPaymentTerm = ?1 and d1.category = 'B' " +
                    "and d1.status = 'CONFIRMED' " +
                    "and cp.paymentType.code = 'monthly_payment')")
    Double findAmountByDdbConfirmed(ContractPaymentTerm cpt);

    @Query("SELECT COUNT (d.id) > 0 " +
            "FROM DirectDebit d " +
            "join d.contractPaymentTerm cpt " +
            "where cpt.isActive = true and cpt.contract = ?1 and d.category = 'B' and d.status in ?2")
    Boolean existsByContractAndCategoryBAndStatusIn(
            Contract c, List<DirectDebitStatus> statuses);

    @Query("select count(d.id) > 0 from DirectDebit d " +
            "where d.contractPaymentTerm = ?1 and d.isDeleted = 0 and " +
                "d.status <> 'CANCELED' AND d.MStatus <> 'CANCELED'")
    Boolean existsDdNotCanceledAndNotDeletedByCpt(ContractPaymentTerm c);

    @Query("Select count(d.id) > 0 from DirectDebit d " +
            "where d.contractPaymentTerm.id = ?1 and d.prorated = true and d.startDate <= ?2 and " +
                "(d.MStatus not in ?3 " +
                    "or exists (select 1 from Payment p " +
                        "where p.contract.id = ?4 and p.status = 'RECEIVED' and p.amountOfPayment = d.amount and " +
                            "p.typeOfPayment.code = 'monthly_payment' and p.dateOfPayment = d.startDate))")
    boolean existsProratedDdAndStartDateAndStatusNotInOrHasPaymentReceived(
            Long cptId, Date date, List<DirectDebitStatus> s, Long contractId);

    @Query("select count(d.id) > 0 " +
            "from DirectDebit d " +
            "join d.contractPaymentTerm cpt " +
            "where cpt.contract = ?1 and cpt.isActive = true " +
                "and (d.status = 'PENDING' or d.MStatus = 'PENDING') " +
                "and exists (select 1 from DirectDebitFile ddf where ddf.directDebit = d and ddf.status = 'SENT')")
    Boolean existsPendingDdWithDdfSent(Contract contract);

    @Query("select count(d.id) > 0 " +
            "from DirectDebit d " +
            "join d.contractPaymentTerm cpt " +
            "where cpt.contract = ?1 and cpt.isActive = true " +
                "and (d.status in ('PENDING', 'PENDING_DATA_ENTRY') " +
                    "or d.MStatus in ('PENDING','PENDING_DATA_ENTRY')) ")
    Boolean existsPendingUserAction(Contract contract);

    @Query("Select count(d.id) > 0 from DirectDebit d " +
           "where d.contractPaymentTerm.contract = ?1 " +
                "and d.status not in (?2) and d.MStatus not in (?2) ")
    Boolean hasActiveDD(Contract contract, List<DirectDebitStatus> notActiveStatus);

    @Query("Select count(d.id) > 0 from DirectDebit d " +
            "where d.contractPaymentTerm = ?1 and " +
                "d.status not in (?2) and d.MStatus not in (?2) ")
    Boolean hasActiveDDByCpt(ContractPaymentTerm cpt, List<DirectDebitStatus> notActiveStatus);

    @Query("Select count(d.id) > 0 from DirectDebit d " +
            "where d.contractPaymentTerm.contract.id = ?1 and d.category = 'B' " +
                "and d.status not in (?2) and d.MStatus not in (?2) ")
    Boolean hasActiveDdb(Long contractId, List<DirectDebitStatus> notActiveStatus);

    @Query("Select count(d.id) > 0 from DirectDebit d where d.contractPaymentTerm = ?1 and d.category = 'B'")
    Boolean hasDdb(ContractPaymentTerm cpt);

    @Query("select d from DirectDebit d " +
            "join d.contractPaymentTerm cpt " +
            "where cpt = ?1 and cpt.isActive = true " +
                "and (d.status in ('PENDING', 'PENDING_DATA_ENTRY') or d.MStatus in ('PENDING', 'PENDING_DATA_ENTRY')) " +
                "and not exists (select 1 from DirectDebitFile ddf where ddf.directDebit = d and " +
                    "ddf.status not in ('NOT_SENT', 'NOT_COMPLETED') and " +
                    "((d.status in ('PENDING', 'PENDING_DATA_ENTRY') and ddf.ddMethod = 'AUTOMATIC') or " +
                    "(d.MStatus in ('PENDING', 'PENDING_DATA_ENTRY') and ddf.ddMethod = 'MANUAL')))")
    List<DirectDebit> findForAcc6197(ContractPaymentTerm cpt);

    @Query("select count(dd.id) > 0 " +
            "from DirectDebit dd " +
            "where dd.contractPaymentTerm = ?1 and ((dd.category = 'A' and dd.MStatus = ?2) or " +
                 "(dd.category = 'B' and (dd.status = ?2 or dd.MStatus = ?2 ))) ")
    boolean existsByActiveContractPaymentTermAndStatus(ContractPaymentTerm cpt, DirectDebitStatus statuses);

    @Query(nativeQuery = true,
            value = "SELECT C.ID as contractId, A.ID as fileId FROM CLIENTS C " +
                    "INNER JOIN CONTRACTS CT ON CT.CLIENT_ID = C.ID " +
                    "INNER JOIN CONTRACTPAYMENTTERMS CPT ON CPT.CONTRACT_ID = CT.ID AND CPT.IS_ACTIVE = 1 " +
                    "INNER JOIN ATTACHMENTS A ON A.OWNER_ID = CPT.ID AND A.OWNER_TYPE = CPT.ENTITY_TYPE AND A.TAG = 'bank_info_eid' " +
                    "LEFT JOIN CLIENTDOCUMENTS D ON D.CLIENT_ID = C.ID AND D.TYPE_ID = " +
                        "(SELECT ID FROM PICKLISTS_ITEMS WHERE CODE = 'EMIRATES_ID_FRONT_SIDE') " +
                    "WHERE D.ID IS NULL AND CT.STATUS = 'ACTIVE' AND C.ID > ?1 " +
                        "AND EXISTS(SELECT ID FROM DIRECTDEBITS " +
                            "WHERE CONTRACT_PAYMENT_TERM_ID = CPT.ID AND CONFIRMED_BANK_INFO = 1) " +
                    "LIMIT 50")
    List<Long[]> migrateEidDocumentQuery(Long id);

    @Query("select d.status, d.MStatus, ddf.status, todo " +
            "from DirectDebit d " +
            "left join DirectDebitFile ddf on ddf.directDebit = d " +
            "left join DirectDebitRejectionToDo todo on todo.id = d.directDebitRejectionToDo " +
            "where d.contractPaymentTerm = ?1 and d.category = 'B' " +
            "order by case " +
                "when d.status = 'IN_COMPLETE' then 0 " +
                "when d.status = 'PENDING_DATA_ENTRY' then 1 " +
                "when d.status = 'PENDING' then 2 " +
                "when d.status = 'CONFIRMED' then 3 " +
                "when d.status = 'REJECTED' then 4 " +
                "when d.status = 'PENDING_FOR_CANCELLATION' then 5 " +
                "when d.status = 'CANCELED' then 6 " +
                "when d.status = 'EXPIRED' then 7 " +
                "else 8 end")
    List<Object[]> findDdbStatusAndDdfStatusByCpt(ContractPaymentTerm cpt);

    @Query(value = "select ddm.STATUS, " +
                "case " +
                    "when ddm.STATUS = 'PENDING_DATA_ENTRY' then dd_r.LAST_MODIFICATION_DATE " +
                    "else (select dd_r1.LAST_MODIFICATION_DATE " +
                            "from DIRECTDEBITS_REVISIONS dd_r1 " +
                            "where dd_r1.ID = ddm.ID and dd_r1.STATUS_MODIFIED = 1 and dd_r1.STATUS = 'PENDING' " +
                            "order by dd_r1.LAST_MODIFICATION_DATE DESC LIMIT 1) " +
                "end " +
            "from DIRECTDEBITS ddm " +
            "inner join CONTRACTPAYMENTTERMS cpt on cpt.id = ddm.CONTRACT_PAYMENT_TERM_ID " +
            "inner join DIRECTDEBITS_REVISIONS dd_r on ddm.ID = dd_r.ID " +
            "where cpt.CONTRACT_ID = ?1 and ddm.STATUS <> 'IN_COMPLETE' " +
                "and dd_r.STATUS_MODIFIED = 1 and dd_r.STATUS = 'PENDING_DATA_ENTRY' " +
            "order by dd_r.LAST_MODIFICATION_DATE desc " +
            "limit 1", nativeQuery = true)
    List<Object[]> findFirstDataEntryToDo(Long contractId);

    @Query("select count(dd.id) > 0 " +
            "from DirectDebit dd " +
            "join dd.contractPaymentTerm.contract as c " +
            "where c.client = ?1 and c.status in ('ACTIVE', 'PLANNED_RENEWAL') and " +
                "((dd.category = 'A' and dd.MStatus = 'CONFIRMED') or " +
                "(dd.category = 'B' and dd.status = 'CONFIRMED'))")
    boolean clientsHasConfirmedDd(Client client);

    @Query("SELECT count (cp.id) > 0 FROM ContractPayment cp " +
            "WHERE cp.paymentType.code = 'monthly_payment' AND cp.directDebit.id = ?1")
    boolean isMonthlyDD(Long ddId);

    @Query("select count(dd.id) = 0 from DirectDebit dd " +
            "where dd.contractPaymentTerm = ?1 and " +
            "((dd.category = 'A' and dd.MStatus <> 'CANCELED') or " +
            "(dd.category = 'B' and dd.status <> 'CANCELED')) ")
    boolean allDdCanceledByCpt(ContractPaymentTerm cpt);

    @Query("select dd from DirectDebitFile ddf " +
            "join ddf.directDebit dd " +
            "join dd.contractPaymentTerm as cpt " +
            "where cpt.contract = ?1 and cpt.isActive = true and dd.category = 'B' and " +
                  "dd.expiryDate > ?2 and ddf.ddStatus = 'CONFIRMED' and ddf.status = 'APPROVED' and ddf.ddMethod = 'AUTOMATIC' and " +
                  "not exists (select 1 from DirectDebitCancelationToDo todo where todo.directDebitFile = ddf and todo.stopped = false and " +
                        "todo.completed = false) " +
            "group by dd.id " +
            "order by dd.startDate asc")
    List<DirectDebit> findConfirmedDDbByActiveCpt(Contract c, Date date);

    @Query("select dd from DirectDebit dd " +
            "where dd.contractPaymentTerm.id = ?1 and " +
            "((dd.category = 'A' and dd.MStatus = 'IN_COMPLETE' ) or (dd.category = 'B' and dd.status = 'IN_COMPLETE'))")
    List<DirectDebit> findAllDDsOfIncompleteFlow(Long cptId);

    @Query("select dd from DirectDebit dd " +
            "join dd.contractPaymentTerm cpt " +
            "join DirectDebitRejectionToDo rejectionToDo on rejectionToDo.id = dd.directDebitRejectionToDo.id or rejectionToDo.id = dd.directDebitBouncingRejectionToDo.id " +
            "where cpt.id = ?1 and rejectionToDo.completed = false and rejectionToDo.stopped = false and " +
            "((dd.category = 'A' and dd.MStatus in ?2) or (dd.category = 'B' and dd.status in ?2))")
    List<DirectDebit> findAllDdOfRejectedFlowByStatuses(Long cptId, List<DirectDebitStatus> activeWithoutConfirmedStatuses);

    @Query("select dd from DirectDebit dd where dd.contractPaymentTerm = ?1 and " +
            "((dd.category = 'A' and dd.MStatus not in ?2) or (dd.category = 'B' and dd.status not in ?2)) and " +
            "exists (select 1 from ContractPayment cp where cp.directDebit.id = dd.id)")
    List<DirectDebit> getAllDDsByContractPaymentTermAndStatus(ContractPaymentTerm contractPaymentTerm, List<DirectDebitStatus> directDebitStatus);

    @Query("select count(d.id) > 0 " +
            "from DirectDebit d " +
            "where d.contractPaymentTerm = ?1 and d.addedManuallyFromClientProfile = 1 and " +
                "d.status not in (?2) and d.MStatus not in (?2)")
    Boolean existsByContractPaymentTermAndAddedManuallyFromClientProfileTrue(ContractPaymentTerm cpt, List<DirectDebitStatus> notActiveStatus);

    @Query("select count(dd.id) > 0 " +
            "from DirectDebit dd " +
            "where dd.contractPaymentTerm = ?1 and " +
                "((dd.category = 'A' and dd.MStatus = 'CONFIRMED') or " +
                "(dd.category = 'B' and dd.status = 'CONFIRMED'))")
    boolean cptHasConfirmedDd(ContractPaymentTerm cpt);

    @Query("select d " +
            "from DirectDebit d  " +
            "join d.contractPaymentTerm cpt " +
            "left join d.directDebitRejectionToDo todo " +
            "left join d.directDebitBouncingRejectionToDo bouncedTodo " +
            "where cpt.contract.id = ?1 and cpt.isActive = false and " +
            "((todo.taskName is not null and todo.completed = false and todo.stopped = false) or " +
            "(bouncedTodo.taskName is not null and bouncedTodo.completed = false and bouncedTodo.stopped = false))")
    List<DirectDebit> findDirectDebitByRejectionFlowAndCptInactive(Long id);


    @Query("select count(dd.id) > 0 " +
            "from DirectDebit dd " +
            "where dd.contractPaymentTerm = ?1 and dd.creationDate >= ?2 and " +
            "   ((dd.category = 'A' and dd.MStatus not in ?3) or " +
            "   (dd.category = 'B' and dd.status not in ?3)) ")
    boolean existsByContractPaymentTermAndActive(ContractPaymentTerm cpt, Date s, List<DirectDebitStatus> statuses);

    @Query("select count(dd.id) > 0 " +
            "from DirectDebit dd " +
            "where dd.contractPaymentTerm = ?1 and dd.creationDate >= ?2 and dd.creationDate <= ?3 and " +
            "   ((dd.category = 'A' and dd.MStatus in ?4) or " +
            "   (dd.category = 'B' and dd.status in ?4)) ")
    boolean existsByContractPaymentTermAndStatusesAndCreationDate(ContractPaymentTerm cpt, Date s, Date e, List<DirectDebitStatus> statuses);

    @Query("select count(cp.id) > 0 " +
            "from ContractPayment cp " +
            "join cp.directDebit d " +
            "where d.contractPaymentTerm.contract = ?1 and cp.paymentType.code = 'monthly_payment' and " +
            "((d.category = 'B' and d.expiryDate >= ?2  and d.status not in ?3) or " +
            "(d.category = 'A' and d.startDate >= ?2 and d.MStatus not in ?3))")
    boolean existsMonthlyDdCoverAfterPaidEndDate(Contract c, Date d, List<DirectDebitStatus> statuses);

    @Query("select count(cp.id) > 0 " +
            "from ContractPayment cp " +
            "join cp.directDebit d " +
            "join d.directDebitRejectionToDo todo " +
            "where d.contractPaymentTerm.contract = ?1 and cp.paymentType.code = 'monthly_payment' and " +
                "((d.category = 'B' and d.expiryDate >= ?2) or (d.category = 'A' and d.startDate >= ?2)) and " +
                "todo.completed = false and todo.stopped = false ")
    boolean existsRejectionFlowOfMonthlyDdCoverAfterPaidEndDate(Contract c, Date d);

    @Query(nativeQuery = true, value =
            "SELECT DISTINCT D.* " +
            "FROM DIRECTDEBITS D " +
            "INNER JOIN CONTRACTPAYMENTTERMS CPT ON CPT.ID = D.CONTRACT_PAYMENT_TERM_ID " +
            "LEFT JOIN DIRECTDEBITCANCELATIONTODOS TODO ON TODO.DIRECT_DEBIT_ID = D.ID " +
            "WHERE CPT.CONTRACT_ID = ?1 AND (TODO.ID IS NULL OR TODO.REASON = 'CONTRACT_CANCELLATION') AND " +
                "((D.CATEGORY = 'A' AND D.M_STATUS IN ('CANCELED', 'PENDING_FOR_CANCELLATION')) OR " +
                    "(D.CATEGORY = 'B' AND D.STATUS IN ('CANCELED', 'PENDING_FOR_CANCELLATION') AND " +
                        "D.M_STATUS IN ('CANCELED', 'PENDING_FOR_CANCELLATION', 'NOT_APPLICABLE'))) AND " +
                "EXISTS( SELECT 1 FROM DIRECTDEBITS_REVISIONS DR_CANCELATION " +
                         "WHERE D.ID = DR_CANCELATION.ID AND DR_CANCELATION.LAST_MODIFICATION_DATE >= ?2 AND " +
                            "((DR_CANCELATION.STATUS_MODIFIED = 1 AND DR_CANCELATION.CATEGORY = 'A' AND " +
                                "DR_CANCELATION.M_STATUS IN ('CANCELED', 'PENDING_FOR_CANCELLATION')) OR " +
                            "((DR_CANCELATION.STATUS_MODIFIED = 1 OR DR_CANCELATION.M_STATUS_MODIFIED = 1) AND DR_CANCELATION.CATEGORY = 'B' AND " +
                                "DR_CANCELATION.STATUS IN ('CANCELED', 'PENDING_FOR_CANCELLATION') AND " +
                                "DR_CANCELATION.M_STATUS IN ('CANCELED', 'PENDING_FOR_CANCELLATION', 'NOT_APPLICABLE')))) " +
            "ORDER BY D.CREATION_DATE DESC")
    List<DirectDebit> findAllCanceledDDForAcc6594(Long contractId, Date dateOfTermination);

    @Query("select count(dd.id) > 0 from DirectDebit dd " +
            "where dd.contractPaymentTerm = ?1 and " +
                "((dd.category = 'A' and dd.MStatus in ?2) or " +
                "(dd.category = 'B' and dd.status in ?2)) ")
    boolean existsActiveDdByCptAndStatus(ContractPaymentTerm cpt, List<DirectDebitStatus> status);

    @Query("select d " +
            "from DirectDebit d  " +
            "join d.contractPaymentTerm cpt " +
            "left join d.directDebitRejectionToDo todo " +
            "left join d.directDebitBouncingRejectionToDo bouncedTodo " +
            "where cpt = ?1 and (d.status = 'REJECTED' or d.MStatus = 'REJECTED') and " +
            "((todo.taskName is not null and todo.completed = false and todo.stopped = false) or " +
            "(bouncedTodo.taskName is not null and bouncedTodo.completed = false and bouncedTodo.stopped = false))")
    List<DirectDebit> findDirectDebitByRejectionFlowByCpt(ContractPaymentTerm cpt);

    @Query("select count(dd.id) > 0 " +
            "from DirectDebit dd " +
            "join dd.contractPaymentTerm.contract as c " +
            "where c = ?1 and dd.category = 'B' and dd.status = 'CONFIRMED'")
    boolean contractHasConfirmedDdb(Contract c);

    @Query("select distinct dd from DirectDebitFile ddf " +
            "join ddf.directDebit dd " +
            "where dd.contractPaymentTerm.contract.id = ?1 and dd.category = 'B' and " +
                "dd.startDate < ?2 and dd.expiryDate > ?3 and dd.status not in ?4 and " +
                "ddf.ddMethod = 'AUTOMATIC' and not exists (select 1 from DirectDebitCancelationToDo todo " +
                    "where todo.directDebitFile = ddf and todo.stopped = false and todo.completed = false)")
    List<DirectDebit> findALlActiveDDbByContract(Long contractId, Date startDate, Date endDate, List<DirectDebitStatus> notAllowedStatuses);

    @Query("select count(dd.id) > 0 " +
            "from DirectDebitFile ddf " +
            "join ddf.directDebit dd " +
            "join dd.contractPaymentTerm cpt " +
            "inner join ContractPayment cp on cp.directDebit = dd " +
            "where cpt.contract.id = ?1 and " +
                "((dd.category = 'A' and dd.MStatus = 'CONFIRMED' and ddf.ddMethod = 'MANUAL') or " +
                    "(dd.category = 'B' and dd.status = 'CONFIRMED' and ddf.ddMethod = 'AUTOMATIC')) and " +
                "cp.paymentType.code = ?2 and cp.date between ?3 and ?4 and " +
                "ddf.ddStatus = 'CONFIRMED' and ddf.status = 'APPROVED' and " +
                    "not exists (select 1 from DirectDebitCancelationToDo todo " +
                                "where todo.directDebitFile = ddf and todo.stopped = false and todo.completed = false)")
    boolean existsDDConfirmedCoveredPaymentWithoutAmount(Long contractId, String type, Date start, Date end);

    @Query("select dd from DirectDebit dd " +
            "where dd.creationDate <= ?1 and dd.hidden = true and dd.ddcId is null and " +
            "dd.contractPaymentTerm.contract.status = 'ACTIVE' and  " +
            "((dd.category = 'A' and dd.MStatus not in ?2) or (dd.category = 'B' and dd.status not in ?2)) ")
    List<DirectDebit> findAllDdHiddenGeneratedBeforeCreationDate(Date s, List<DirectDebitStatus> notAllowedStatuses);

    @Query("select dd from DirectDebit dd " +
            "inner join AppsServiceDDApprovalTodo ddc on ddc.id = dd.ddcId " +
            "where dd.hidden = true and ddc.result = 'CLOSED_WITH_CONFIRMATION' ")
    List<DirectDebit> findAllDdHiddenRelatedWithClosedDdc();

    @Query("select dd from DirectDebit dd " +
            "inner join AppsServiceDDApprovalTodo ddc on ddc.id = dd.ddcId " +
            "where dd.hidden = true and ddc.isClosed = false and " +
            "((dd.category = 'A' and dd.MStatus in ?1) or (dd.category = 'B' and dd.status in ?1))")
    List<DirectDebit> findAllDdCanceledAndHiddenRelatedWithDdc(List<DirectDebitStatus> statuses);

    @Query("select count(dd.id) > 0 from DirectDebit dd " +
            "where dd.id <> ?1 and dd.ddcId = ?2 and " +
            "((dd.category = 'A' and dd.MStatus not in ?3) or (dd.category = 'B' and dd.status not in ?3))")
    boolean existsOtherActiveDdsRelatedToSameDdc(Long ddId, Long ddcId, List<DirectDebitStatus> statuses);

    @Query("select dd " +
            "from ContractPayment cp  " +
            "join cp.directDebit dd " +
            "where dd.contractPaymentTerm.contract = ?1 and cp.paymentType.code = 'insurance' and " +
                "dd.category = 'A' and dd.MStatus not in ?2")
    List<DirectDebit> findInsuranceDDGeneratedAndNotCollected(Contract contract, List<DirectDebitStatus> l);

    @Query("select distinct dd from ContractPayment cp " +
            "join cp.directDebit dd " +
            "where dd.contractPaymentTerm.contract = ?1 and cp.includeWorkerSalary = true and " +
                "cp.paymentType.code = 'monthly_payment' and " +
                "((dd.category = 'A' and dd.MStatus not in ?2) or " +
                "(dd.category = 'B' and (dd.status not in ?2 or dd.MStatus not in ?2)))")
    List<DirectDebit> getMonthlyDirectDebitIncludedWorkerSalary(Contract c, List<DirectDebitStatus> statuses);

    @Query("select count(dd.id) > 0 " +
            "from DirectDebit dd " +
            "inner join ContractPayment cp on cp.directDebit = dd " +
            "where dd.contractPaymentTerm.contract = ?1 and cp.paymentType.code = 'monthly_payment' and " +
            "dd.category = 'B' and cp.date >= ?2 and cp.date <= ?3 and dd.status not in ?4")
    boolean isDdbCoverNextMonth(Contract c, Date s, Date e, List<DirectDebitStatus> statuses);

    @Query("select dd " +
            "from DirectDebit dd " +
            "inner join ContractPayment cp on cp.directDebit = dd " +
            "where dd.contractPaymentTerm.contract = ?1 and cp.paymentType.code = 'monthly_payment' and " +
            "dd.category = 'A' and cp.date >= ?2 and cp.date <= ?3 and dd.MStatus not in ?4")
    List<DirectDebit> getActiveDdaByMonth(Contract c, Date s, Date e, List<DirectDebitStatus> statuses);
}